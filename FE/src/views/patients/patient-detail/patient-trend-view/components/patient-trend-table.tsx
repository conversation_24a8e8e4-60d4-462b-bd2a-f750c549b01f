import clsx from 'clsx';
import {CSSProperties, useMemo, useState} from 'react';
import {Panel, PanelGroup, PanelResizeHandle} from 'react-resizable-panels';
import {useParams} from 'react-router';

import {useWindowScroll} from '@mantine/hooks';
import {ColDef, iconSetMaterial, themeQuartz} from 'ag-grid-enterprise';
import {AgGridReact} from 'ag-grid-react';
import {format, parse} from 'date-fns';
import {observer} from 'mobx-react-lite';

import {useGlobalStoreLoader} from '@/store/global.store.ts';
import TrendViewGraph from '@/views/patients/patient-detail/patient-trend-view/components/trend-view-graph.tsx';
import useComputeRftTrendData from '@/views/patients/patient-detail/patient-trend-view/use-compute-rft-trend-data.ts';

export interface ParameterSelection {
  name: string;
  id: string;
  values: (number | undefined)[];
  dates: string[];
  percentages: (number | undefined)[];
}

const selectionDisabledAttr = [
  'Date',
  'Time',
  'Test Type',
  'Pre-condition',
  'Post Condition',
  'Report',
  'Technical Note',
];

const PatientTrendTable = observer(() => {
  useGlobalStoreLoader();
  const {patientId} = useParams();
  const [scroll] = useWindowScroll();
  const [parameterSelection, setParameterSelection] = useState<ParameterSelection[] | []>([]);

  const {stores, isLoading, rftHeaders} = useComputeRftTrendData({patientId: patientId ?? ''});

  const filteredHeaders = useMemo(() => {
    return rftHeaders.filter((header) => !header.id.includes('Test Type'));
  }, [rftHeaders]);

  const columnDefs = useMemo<ColDef[]>(() => {
    const testTypeColumn: ColDef = {
      field: 'testType',
      headerName: 'Test Type',
      hide: true,
      rowGroup: true,
      suppressMovable: true,
    };

    const headerColumn: ColDef = {
      headerName: 'PARAMETER',
      field: 'rowHeader',
      pinned: 'left',
      suppressHeaderMenuButton: true,
      width: 232,
      minWidth: 180,
      cellRenderer: (params: any) => {
        const header = filteredHeaders[params?.data?.rowIndex];
        if (!header) return '';
        return (
          <div
            className={clsx('flex items-center font-semibold uppercase', {
              'pl-6': params.node.level > 0,
            })}
          >
            {header.name}
            {header.unit && <span className="ml-1 text-neutral-500">[{header.unit}]</span>}
          </div>
        );
      },
      suppressMovable: true,
      rowGroup: false,
      checkboxSelection: (params: any) => {
        if (params.node.group) return false;
        const header = filteredHeaders[params?.data?.rowIndex];
        return header && !selectionDisabledAttr.includes(header.name);
      },
    };

    const dataColumns: ColDef[] = stores.map((store, index) => ({
      headerName: store.test.testdate
        ? store.test.testtime
          ? format(
              parse(`${store.test.testdate} ${store.test.testtime}`, 'yyyy-MM-dd HH:mm:ss', new Date()),
              'dd MMM yyyy HH:mm'
            )
          : format(parse(store.test.testdate, 'yyyy-MM-dd', new Date()), 'dd MMM yyyy')
        : `Session ${index + 1}`,
      field: `session_${(store.test as any).sessionid}`,
      suppressHeaderMenuButton: true,
      minWidth: 135,
      width: 141,
      valueGetter: (params: any) => {
        if (params.node.group) return '';
        const header = filteredHeaders[params?.data?.rowIndex];
        if (!header) {
          console.warn(`No header found for rowIndex: ${params?.data?.rowIndex}`);
          return '';
        }
        if (!header.render) {
          console.warn(`No render function for header: ${header.name}`);
          return '';
        }
        const value = header.render(params?.data[params.colDef.field]);
        return value ?? '-';
      },
      tooltipValueGetter: (params: any) =>
        params.value !== '-' && params?.value?.length > 20 ? params.value : null,
      cellRenderer: (params: any) => {
        return params.value ? params.value : '-';
      },
    }));

    return [testTypeColumn, headerColumn, ...dataColumns];
  }, [stores, filteredHeaders]);

  const rowData = useMemo(() => {
    const validRows = filteredHeaders
      .map((header, index) => {
        const row = {
          rowIndex: index,
          rowHeader: header.name,
          testType: header.testType || 'General',
          ...stores.reduce(
            (acc, store) => {
              acc[`session_${(store?.test as any)?.sessionid}`] = store;
              return acc;
            },
            {} as Record<string, any>
          ),
        };

        const hasValidData = stores.some((store) => {
          const value = header.render ? header.render(store) : undefined;
          return value !== undefined && value !== null && value !== '-';
        });

        return hasValidData ? row : null;
      })
      .filter((row) => row !== null); // Remove rows with no valid data

    const validTestTypes = new Set(validRows.map((row) => row!.testType));

    return validRows.filter((row) => validTestTypes.has(row!.testType)) as any[];
  }, [filteredHeaders, stores]);

  const myTheme = useMemo(
    () =>
      themeQuartz.withPart(iconSetMaterial).withParams({
        fontFamily: 'Figtree, sans-serif',
        fontSize: 'var(--text-sm)',
        rowHoverColor: 'color-mix(in oklab, var(--color-muted) 50%, transparent)',
        backgroundColor: 'var(--color-white)',
        columnBorder: false,
        headerFontSize: 'var(--text-xs)',
        headerFontWeight: 'bold',
        headerTextColor: 'var(--color-neutral-700)',
        headerHeight: 48,
        iconSize: 13,
        rowHeight: 32,
        headerBackgroundColor: 'var(--color-white)',
        borderColor: 'var(--color-neutral-200)',
        textColor: 'var(--color-neutral-700)',
        rangeSelectionBorderColor: 'var(--color-brand2-500)',
        rangeHeaderHighlightColor: 'var(--color-brand2-500)',
        accentColor: 'var(--color-brand-500)',
        rowBorder: true,
        selectCellBorder: true,
        wrapperBorder: true,
        borderRadius: 0,
        cellEditingShadow: false,
        selectCellBackgroundColor: 'var(--color-white)',
        cellHorizontalPadding: 8,
        selectedRowBackgroundColor: 'var(--color-brand-50)',
        checkboxCheckedBackgroundColor: 'var(--color-brand-500)',
        checkboxCheckedBorderColor: 'var(--color-brand-500)',
        checkboxBorderRadius: '2px',
      }),
    []
  );

  return (
    <>
      {rowData.length > 0 ? (
        <div
          className="sticky top-11.5 isolate z-30 w-full overflow-y-auto overscroll-contain"
          style={
            {
              height: `min(100vh - ${Math.max(206 - scroll.y, 0)}px, 100vh - 66px)`,
            } as CSSProperties
          }
        >
          <PanelGroup
            direction="vertical"
            className="h-full"
          >
            <Panel
              defaultSize={parameterSelection.length ? 60 : 100}
              minSize={30}
            >
              <div className="h-full w-full">
                <AgGridReact
                  className="compact-ag-grid h-full w-full"
                  columnDefs={columnDefs}
                  rowData={rowData}
                  theme={myTheme}
                  loading={isLoading}
                  getRowId={(params) => String(params.data.rowIndex)}
                  animateRows={true}
                  pagination={false}
                  suppressRowVirtualisation={true}
                  suppressContextMenu={true}
                  suppressColumnVirtualisation={true}
                  groupDisplayType="groupRows"
                  groupDefaultExpanded={1}
                  rowSelection="multiple"
                  suppressRowClickSelection={true}
                  onSelectionChanged={(event) => {
                    const selectedRows = event.api.getSelectedRows();
                    const selectedKeys = selectedRows.map((row) => filteredHeaders[row.rowIndex].id);

                    const selectedParameters = filteredHeaders
                      .filter((header) => selectedKeys.includes(header.id))
                      .map((header) => {
                        const earliestToLatestStores = [...stores].sort((a, b) => {
                          const aDate = a.test.testdate;
                          const bDate = b.test.testdate;
                          return aDate.localeCompare(bDate);
                        });
                        const values = earliestToLatestStores.map((store) => header?.render && header.render(store));
                        const percentages = values.map((value) =>
                          value && values[0] ? Math.round((value / values[0]) * 100) : undefined
                        );

                        return {
                          name: header.name,
                          id: header.id,
                          dates: earliestToLatestStores.map((store) => store.test.testdate),
                          values,
                          percentages,
                        };
                      });
                    setParameterSelection(selectedParameters);
                  }}
                />
              </div>
            </Panel>

            {parameterSelection.length > 0 && (
              <>
                <PanelResizeHandle className="flex h-1.5 cursor-row-resize items-center justify-center p-2">
                  <div className="my-0.5 h-1 w-10 rounded-full bg-neutral-400" />
                </PanelResizeHandle>

                <Panel
                  defaultSize={40}
                  minSize={20}
                >
                  <TrendViewGraph
                    parameterSelection={parameterSelection}
                    isLoading={isLoading}
                  />

                  <div className="absolute -top-120 -z-10 h-100 w-full">
                    <TrendViewGraph
                      id="trend-view-graph"
                      hidden={true}
                      parameterSelection={parameterSelection}
                      isLoading={isLoading}
                    />
                  </div>
                </Panel>
              </>
            )}
          </PanelGroup>
        </div>
      ) : (
        <div className="flex h-150 flex-1 flex-col items-center justify-center space-y-2">
          <img
            src="/no-trend-selection.svg"
            alt="Empty Folder"
            className="h-32.5 w-32.5"
          />
          <div className="text-center">
            <div className="text-xl font-bold text-neutral-900 capitalize">no trend data to show</div>
            <div className="text-sm text-neutral-700 capitalize">
              no trend data available for this patient
            </div>
          </div>
        </div>
      )}
    </>
  );
});

export default PatientTrendTable;
